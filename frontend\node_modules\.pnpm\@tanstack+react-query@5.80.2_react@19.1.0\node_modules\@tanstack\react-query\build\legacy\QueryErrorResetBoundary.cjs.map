{"version": 3, "sources": ["../../src/QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AAkDnB;AArCJ,SAAS,cAA4C;AACnD,MAAI,UAAU;AACd,SAAO;AAAA,IACL,YAAY,MAAM;AAChB,gBAAU;AAAA,IACZ;AAAA,IACA,OAAO,MAAM;AACX,gBAAU;AAAA,IACZ;AAAA,IACA,SAAS,MAAM;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAM,iCAAuC,oBAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,MAClC,iBAAW,8BAA8B;AAY1C,IAAM,0BAA0B,CAAC;AAAA,EACtC;AACF,MAAoC;AAClC,QAAM,CAAC,KAAK,IAAU,eAAS,MAAM,YAAY,CAAC;AAClD,SACE,4CAAC,+BAA+B,UAA/B,EAAwC,OACtC,iBAAO,aAAa,aAAa,SAAS,KAAK,IAAI,UACtD;AAEJ;", "names": []}