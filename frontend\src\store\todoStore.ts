import { create } from 'zustand';
import { Todo } from '../services/api';

export type FilterType = 'all' | 'active' | 'completed';

interface TodoStore {
  todos: Todo[];
  filter: FilterType;
  
  // Actions
  setTodos: (todos: Todo[]) => void;
  addTodo: (todo: Todo) => void;
  updateTodo: (id: string, updates: Partial<Todo>) => void;
  removeTodo: (id: string) => void;
  setFilter: (filter: FilterType) => void;
  reorderTodos: (todos: Todo[]) => void;
  
  // Computed values
  filteredTodos: () => Todo[];
  activeCount: () => number;
  completedCount: () => number;
}

export const useTodoStore = create<TodoStore>((set, get) => ({
  todos: [],
  filter: 'all',
  
  setTodos: (todos) => set({ todos }),
  
  addTodo: (todo) => set((state) => ({
    todos: [...state.todos, todo]
  })),
  
  updateTodo: (id, updates) => set((state) => ({
    todos: state.todos.map(todo => 
      todo._id === id ? { ...todo, ...updates } : todo
    )
  })),
  
  removeTodo: (id) => set((state) => ({
    todos: state.todos.filter(todo => todo._id !== id)
  })),
  
  setFilter: (filter) => set({ filter }),
  
  reorderTodos: (todos) => set({ todos }),
  
  filteredTodos: () => {
    const { todos, filter } = get();
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  },
  
  activeCount: () => {
    const { todos } = get();
    return todos.filter(todo => !todo.completed).length;
  },
  
  completedCount: () => {
    const { todos } = get();
    return todos.filter(todo => todo.completed).length;
  },
}));
