import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Todo } from '../services/api';
import { useTodos } from '../hooks/useTodos';
import iconCheck from '/images/icon-check.svg';
import iconCross from '/images/icon-cross.svg';

interface TodoItemProps {
  todo: Todo;
}

const TodoItem = ({ todo }: TodoItemProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(todo.text);
  const { updateTodo, deleteTodo, isUpdating, isDeleting } = useTodos();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: todo._id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleToggleComplete = () => {
    updateTodo(todo._id, { completed: !todo.completed });
  };

  const handleDelete = () => {
    deleteTodo(todo._id);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditText(todo.text);
  };

  const handleSaveEdit = () => {
    if (editText.trim() && editText.trim() !== todo.text) {
      updateTodo(todo._id, { text: editText.trim() });
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditText(todo.text);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`group flex items-center gap-4 p-4 bg-white dark:bg-Very-Dark-Desaturated-Blue border-b border-Very-Light-Grayish-Blue dark:border-Very-Dark-Grayish-Blue3 transition-all duration-200 ${
        isDragging ? 'opacity-50' : ''
      } ${isUpdating || isDeleting ? 'opacity-50' : ''}`}
    >
      {/* Drag handle */}
      <div
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-1 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <div className="w-2 h-4 flex flex-col justify-center gap-0.5">
          <div className="w-full h-0.5 bg-Dark-Grayish-Blue dark:bg-Dark-Grayish-Blue2 rounded"></div>
          <div className="w-full h-0.5 bg-Dark-Grayish-Blue dark:bg-Dark-Grayish-Blue2 rounded"></div>
          <div className="w-full h-0.5 bg-Dark-Grayish-Blue dark:bg-Dark-Grayish-Blue2 rounded"></div>
        </div>
      </div>

      {/* Checkbox */}
      <button
        onClick={handleToggleComplete}
        disabled={isUpdating}
        className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
          todo.completed
            ? 'bg-gradient-to-br from-[hsl(192,100%,67%)] to-[hsl(280,87%,65%)] border-transparent'
            : 'border-Light-Grayish-Blue dark:border-Very-Dark-Grayish-Blue3 hover:border-Bright-Blue'
        }`}
      >
        {todo.completed && (
          <img src={iconCheck} alt="Completed" className="w-3 h-3" />
        )}
      </button>

      {/* Todo text */}
      <div className="flex-1 min-w-0">
        {isEditing ? (
          <input
            type="text"
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
            onBlur={handleSaveEdit}
            onKeyDown={handleKeyDown}
            className="w-full bg-transparent text-Very-Dark-Grayish-Blue dark:text-Light-Grayish-Blue2 focus:outline-none"
            autoFocus
          />
        ) : (
          <span
            onClick={handleEdit}
            className={`block cursor-text transition-all duration-200 ${
              todo.completed
                ? 'line-through text-Light-Grayish-Blue dark:text-Dark-Grayish-Blue2'
                : 'text-Very-Dark-Grayish-Blue dark:text-Light-Grayish-Blue2'
            }`}
          >
            {todo.text}
          </span>
        )}
      </div>

      {/* Delete button */}
      <button
        onClick={handleDelete}
        disabled={isDeleting}
        className="flex-shrink-0 w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-110"
      >
        <img src={iconCross} alt="Delete" className="w-full h-full" />
      </button>
    </div>
  );
};

export default TodoItem;
