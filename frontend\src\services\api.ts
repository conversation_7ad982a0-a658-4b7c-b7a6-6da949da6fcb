import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface Todo {
  _id: string;
  text: string;
  completed: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTodoRequest {
  text: string;
}

export interface UpdateTodoRequest {
  text?: string;
  completed?: boolean;
}

export interface ReorderTodosRequest {
  todoIds: string[];
}

// API functions
export const todoApi = {
  // Get all todos
  getTodos: async (): Promise<Todo[]> => {
    const response = await api.get('/todos');
    return response.data;
  },

  // Create a new todo
  createTodo: async (data: CreateTodoRequest): Promise<Todo> => {
    const response = await api.post('/todos', data);
    return response.data;
  },

  // Update a todo
  updateTodo: async (id: string, data: UpdateTodoRequest): Promise<Todo> => {
    const response = await api.put(`/todos/${id}`, data);
    return response.data;
  },

  // Delete a todo
  deleteTodo: async (id: string): Promise<void> => {
    await api.delete(`/todos/${id}`);
  },

  // Reorder todos
  reorderTodos: async (data: ReorderTodosRequest): Promise<Todo[]> => {
    const response = await api.put('/todos/reorder', data);
    return response.data;
  },

  // Delete all completed todos
  deleteCompletedTodos: async (): Promise<{ deletedCount: number }> => {
    const response = await api.delete('/todos/completed');
    return response.data;
  },
};

export default api;
