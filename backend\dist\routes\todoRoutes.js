"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Todo_1 = __importDefault(require("../models/Todo"));
const router = express_1.default.Router();
// GET /api/todos - Get all todos
router.get('/', async (req, res) => {
    try {
        const todos = await Todo_1.default.find().sort({ order: 1, createdAt: 1 });
        res.json(todos);
    }
    catch (error) {
        console.error('Error fetching todos:', error);
        res.status(500).json({ error: 'Failed to fetch todos' });
    }
});
// POST /api/todos - Create a new todo
router.post('/', async (req, res) => {
    try {
        const { text } = req.body;
        if (!text || text.trim() === '') {
            return res.status(400).json({ error: 'Todo text is required' });
        }
        // Get the highest order number and increment by 1
        const lastTodo = await Todo_1.default.findOne().sort({ order: -1 });
        const order = lastTodo ? lastTodo.order + 1 : 0;
        const todo = new Todo_1.default({
            text: text.trim(),
            order
        });
        const savedTodo = await todo.save();
        res.status(201).json(savedTodo);
    }
    catch (error) {
        console.error('Error creating todo:', error);
        res.status(500).json({ error: 'Failed to create todo' });
    }
});
// PUT /api/todos/:id - Update a todo
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { text, completed } = req.body;
        const updateData = {};
        if (text !== undefined)
            updateData.text = text.trim();
        if (completed !== undefined)
            updateData.completed = completed;
        const todo = await Todo_1.default.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
        if (!todo) {
            return res.status(404).json({ error: 'Todo not found' });
        }
        res.json(todo);
    }
    catch (error) {
        console.error('Error updating todo:', error);
        res.status(500).json({ error: 'Failed to update todo' });
    }
});
// DELETE /api/todos/:id - Delete a todo
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const todo = await Todo_1.default.findByIdAndDelete(id);
        if (!todo) {
            return res.status(404).json({ error: 'Todo not found' });
        }
        res.json({ message: 'Todo deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting todo:', error);
        res.status(500).json({ error: 'Failed to delete todo' });
    }
});
// PUT /api/todos/reorder - Reorder todos
router.put('/reorder', async (req, res) => {
    try {
        const { todoIds } = req.body;
        if (!Array.isArray(todoIds)) {
            return res.status(400).json({ error: 'todoIds must be an array' });
        }
        // Update the order of each todo
        const updatePromises = todoIds.map((id, index) => Todo_1.default.findByIdAndUpdate(id, { order: index }));
        await Promise.all(updatePromises);
        // Return updated todos
        const todos = await Todo_1.default.find().sort({ order: 1 });
        res.json(todos);
    }
    catch (error) {
        console.error('Error reordering todos:', error);
        res.status(500).json({ error: 'Failed to reorder todos' });
    }
});
// DELETE /api/todos/completed - Delete all completed todos
router.delete('/completed', async (req, res) => {
    try {
        const result = await Todo_1.default.deleteMany({ completed: true });
        res.json({
            message: `${result.deletedCount} completed todos deleted successfully`,
            deletedCount: result.deletedCount
        });
    }
    catch (error) {
        console.error('Error deleting completed todos:', error);
        res.status(500).json({ error: 'Failed to delete completed todos' });
    }
});
exports.default = router;
//# sourceMappingURL=todoRoutes.js.map