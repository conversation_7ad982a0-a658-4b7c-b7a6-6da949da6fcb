import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useTodoStore } from '../store/todoStore';
import { useTodos } from '../hooks/useTodos';
import TodoItem from './TodoItem';

const TodoList = () => {
  const { filteredTodos } = useTodoStore();
  const { reorderTodos, isReordering } = useTodos();
  
  const todos = filteredTodos();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = todos.findIndex((todo) => todo._id === active.id);
      const newIndex = todos.findIndex((todo) => todo._id === over.id);

      const reorderedTodos = arrayMove(todos, oldIndex, newIndex);
      const todoIds = reorderedTodos.map((todo) => todo._id);
      
      reorderTodos({ todoIds });
    }
  };

  if (todos.length === 0) {
    return (
      <div className="bg-white dark:bg-Very-Dark-Desaturated-Blue rounded-lg shadow-lg">
        <div className="p-8 text-center text-Dark-Grayish-Blue dark:text-Dark-Grayish-Blue2">
          No todos found. Create your first todo above!
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-Very-Dark-Desaturated-Blue rounded-lg shadow-lg overflow-hidden ${
      isReordering ? 'opacity-75' : ''
    }`}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={todos.map(todo => todo._id)} strategy={verticalListSortingStrategy}>
          {todos.map((todo) => (
            <TodoItem key={todo._id} todo={todo} />
          ))}
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default TodoList;
