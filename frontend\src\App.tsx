import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Header from "./components/Header";
import TodoInput from "./components/TodoInput";
import TodoList from "./components/TodoList";
import TodoFilters from "./components/TodoFilters";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-Very-Light-Gray dark:bg-Very-Dark-Blue transition-colors duration-300">
        <Header />

        <div className="relative -mt-24 md:-mt-32 z-10">
          <div className="max-w-xl mx-auto px-6">
            <TodoInput />
            <div className="space-y-6">
              <TodoList />
              <TodoFilters />
            </div>

            {/* Drag and drop instruction */}
            <p className="text-center text-sm text-Dark-Grayish-Blue dark:text-Dark-Grayish-Blue2 mt-12">
              Drag and drop to reorder list
            </p>
          </div>
        </div>
      </div>
    </QueryClientProvider>
  );
};

export default App;
