import { useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

interface Todo {
  id: string;
  text: string;
  completed: boolean;
}

type FilterType = "all" | "active" | "completed";

// Sortable Todo Item Component
const SortableTodoItem = ({
  todo,
  isDark,
  toggleTodo,
  deleteTodo,
}: {
  todo: Todo;
  isDark: boolean;
  toggleTodo: (id: string) => void;
  deleteTodo: (id: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: todo.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      className="group flex items-center gap-4 p-4 last:border-b-0"
      style={{
        ...style,
        borderBottom: `1px solid ${
          isDark ? "hsl(237, 14%, 26%)" : "hsl(236, 33%, 92%)"
        }`,
      }}
    >
      {/* Drag handle */}
      <div
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-1 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <div className="w-2 h-4 flex flex-col justify-center gap-0.5">
          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
        </div>
      </div>

      {/* Checkbox */}
      <button
        onClick={() => toggleTodo(todo.id)}
        className="flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:border-opacity-80"
        style={{
          background: todo.completed
            ? "linear-gradient(135deg, hsl(192, 100%, 67%) 0%, hsl(280, 87%, 65%) 100%)"
            : "transparent",
          borderColor: todo.completed
            ? "transparent"
            : isDark
            ? "hsl(237, 14%, 26%)"
            : "hsl(233, 11%, 84%)",
        }}
      >
        {todo.completed && (
          <img
            src="/images/icon-check.svg"
            alt="Completed"
            className="w-3 h-3"
          />
        )}
      </button>

      {/* Todo text */}
      <span
        className={`flex-1 transition-all duration-200 ${
          todo.completed ? "line-through" : ""
        }`}
        style={{
          color: todo.completed
            ? isDark
              ? "hsl(233, 14%, 35%)"
              : "hsl(233, 11%, 84%)"
            : isDark
            ? "hsl(234, 39%, 85%)"
            : "hsl(235, 19%, 35%)",
          fontSize: "18px",
        }}
      >
        {todo.text}
      </span>

      {/* Delete button */}
      <button
        onClick={() => deleteTodo(todo.id)}
        className="flex-shrink-0 w-6 h-6 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity duration-200"
      >
        <img
          src="/images/icon-cross.svg"
          alt="Delete"
          className="w-full h-full"
        />
      </button>
    </div>
  );
};

const App = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [inputText, setInputText] = useState("");
  const [isDark, setIsDark] = useState(false);
  const [filter, setFilter] = useState<FilterType>("all");

  const addTodo = () => {
    if (inputText.trim()) {
      const newTodo: Todo = {
        id: Date.now().toString(),
        text: inputText.trim(),
        completed: false,
      };
      setTodos([...todos, newTodo]);
      setInputText("");
    }
  };

  const toggleTodo = (id: string) => {
    setTodos(
      todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const deleteTodo = (id: string) => {
    setTodos(todos.filter((todo) => todo.id !== id));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      addTodo();
    }
  };

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = todos.findIndex((todo) => todo.id === active.id);
      const newIndex = todos.findIndex((todo) => todo.id === over.id);

      setTodos(arrayMove(todos, oldIndex, newIndex));
    }
  };

  // Filter todos based on current filter
  const filteredTodos = todos.filter((todo) => {
    switch (filter) {
      case "active":
        return !todo.completed;
      case "completed":
        return todo.completed;
      default:
        return true;
    }
  });

  return (
    <QueryClientProvider client={queryClient}>
      <div
        className={`min-h-screen transition-colors duration-300 ${
          isDark ? "dark" : ""
        }`}
        style={{
          backgroundColor: isDark ? "hsl(235, 21%, 11%)" : "hsl(0, 0%, 98%)",
        }}
      >
        {/* Header with background image */}
        <div
          className="relative h-[200px] md:h-[300px] bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: isDark
              ? window.innerWidth >= 768
                ? "url('/images/bg-desktop-dark.jpg')"
                : "url('/images/bg-mobile-dark.jpg')"
              : window.innerWidth >= 768
              ? "url('/images/bg-desktop-light.jpg')"
              : "url('/images/bg-mobile-light.jpg')",
          }}
        >
          <div className="absolute inset-0 flex items-start justify-center pt-12 md:pt-16">
            <div className="w-full max-w-xl px-6">
              <div className="flex items-center justify-between mb-8">
                <h1 className="text-2xl md:text-4xl font-bold text-white tracking-[0.3em] md:tracking-[0.5em]">
                  TODO
                </h1>
                <button
                  onClick={() => setIsDark(!isDark)}
                  className="transition duration-300 hover:scale-110 p-1"
                >
                  <img
                    src={
                      isDark ? "/images/icon-sun.svg" : "/images/icon-moon.svg"
                    }
                    alt="Toggle theme"
                    className="w-6 h-6"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div
          className="relative -mt-24 md:-mt-32 z-10 min-h-screen"
          style={{
            backgroundColor: isDark ? "hsl(235, 21%, 11%)" : "hsl(0, 0%, 98%)",
          }}
        >
          <div className="max-w-xl mx-auto px-6 pt-8">
            {/* Todo Input */}
            <div className="relative mb-6">
              <div className="relative">
                <div
                  className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full border-2"
                  style={{
                    borderColor: isDark
                      ? "hsl(237, 14%, 26%)"
                      : "hsl(233, 11%, 84%)",
                  }}
                ></div>
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Create a new todo..."
                  className="w-full pl-16 pr-6 py-4 text-lg border-none rounded-lg shadow-lg focus:outline-none transition-all duration-200"
                  style={{
                    backgroundColor: isDark ? "hsl(235, 24%, 19%)" : "white",
                    color: isDark ? "hsl(234, 39%, 85%)" : "hsl(235, 19%, 35%)",
                    fontSize: "18px",
                  }}
                />
              </div>
            </div>

            {/* Todo List */}
            <div
              className="rounded-lg shadow-lg overflow-hidden mb-6"
              style={{
                backgroundColor: isDark ? "hsl(235, 24%, 19%)" : "white",
              }}
            >
              {filteredTodos.length === 0 ? (
                <div
                  className="p-8 text-center"
                  style={{
                    color: isDark ? "hsl(234, 11%, 52%)" : "hsl(236, 9%, 61%)",
                  }}
                >
                  {todos.length === 0
                    ? "No todos found. Create your first todo above!"
                    : `No ${filter} todos found.`}
                </div>
              ) : (
                filteredTodos.map((todo) => (
                  <div
                    key={todo.id}
                    className="group flex items-center gap-4 p-4 last:border-b-0"
                    style={{
                      borderBottom: `1px solid ${
                        isDark ? "hsl(237, 14%, 26%)" : "hsl(236, 33%, 92%)"
                      }`,
                    }}
                  >
                    {/* Checkbox */}
                    <button
                      onClick={() => toggleTodo(todo.id)}
                      className="flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:border-opacity-80"
                      style={{
                        background: todo.completed
                          ? "linear-gradient(135deg, hsl(192, 100%, 67%) 0%, hsl(280, 87%, 65%) 100%)"
                          : "transparent",
                        borderColor: todo.completed
                          ? "transparent"
                          : isDark
                          ? "hsl(237, 14%, 26%)"
                          : "hsl(233, 11%, 84%)",
                      }}
                    >
                      {todo.completed && (
                        <img
                          src="/images/icon-check.svg"
                          alt="Completed"
                          className="w-3 h-3"
                        />
                      )}
                    </button>

                    {/* Todo text */}
                    <span
                      className={`flex-1 transition-all duration-200 ${
                        todo.completed ? "line-through" : ""
                      }`}
                      style={{
                        color: todo.completed
                          ? isDark
                            ? "hsl(233, 14%, 35%)"
                            : "hsl(233, 11%, 84%)"
                          : isDark
                          ? "hsl(234, 39%, 85%)"
                          : "hsl(235, 19%, 35%)",
                        fontSize: "18px",
                      }}
                    >
                      {todo.text}
                    </span>

                    {/* Delete button */}
                    <button
                      onClick={() => deleteTodo(todo.id)}
                      className="flex-shrink-0 w-6 h-6 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity duration-200"
                    >
                      <img
                        src="/images/icon-cross.svg"
                        alt="Delete"
                        className="w-full h-full"
                      />
                    </button>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            {todos.length > 0 && (
              <div
                className="rounded-lg shadow-lg p-4"
                style={{
                  backgroundColor: isDark ? "hsl(235, 24%, 19%)" : "white",
                }}
              >
                <div className="flex items-center justify-between text-sm">
                  <span
                    style={{
                      color: isDark
                        ? "hsl(234, 11%, 52%)"
                        : "hsl(236, 9%, 61%)",
                    }}
                  >
                    {todos.filter((t) => !t.completed).length} items left
                  </span>

                  {/* Filter buttons */}
                  <div className="flex gap-4">
                    <button
                      onClick={() => setFilter("all")}
                      className="font-bold transition-colors hover:opacity-80"
                      style={{
                        color:
                          filter === "all"
                            ? "hsl(220, 98%, 61%)"
                            : isDark
                            ? "hsl(234, 11%, 52%)"
                            : "hsl(236, 9%, 61%)",
                      }}
                    >
                      All
                    </button>
                    <button
                      onClick={() => setFilter("active")}
                      className="font-bold transition-colors hover:opacity-80"
                      style={{
                        color:
                          filter === "active"
                            ? "hsl(220, 98%, 61%)"
                            : isDark
                            ? "hsl(234, 11%, 52%)"
                            : "hsl(236, 9%, 61%)",
                      }}
                    >
                      Active
                    </button>
                    <button
                      onClick={() => setFilter("completed")}
                      className="font-bold transition-colors hover:opacity-80"
                      style={{
                        color:
                          filter === "completed"
                            ? "hsl(220, 98%, 61%)"
                            : isDark
                            ? "hsl(234, 11%, 52%)"
                            : "hsl(236, 9%, 61%)",
                      }}
                    >
                      Completed
                    </button>
                  </div>

                  <button
                    onClick={() => setTodos(todos.filter((t) => !t.completed))}
                    className="transition-colors hover:opacity-80"
                    style={{
                      color: isDark
                        ? "hsl(234, 11%, 52%)"
                        : "hsl(236, 9%, 61%)",
                    }}
                  >
                    Clear Completed
                  </button>
                </div>
              </div>
            )}

            <p
              className="text-center text-sm mt-12"
              style={{
                color: isDark ? "hsl(234, 11%, 52%)" : "hsl(236, 9%, 61%)",
              }}
            >
              Drag and drop to reorder list
            </p>
          </div>
        </div>
      </div>
    </QueryClientProvider>
  );
};

export default App;
