import { useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

interface Todo {
  id: string;
  text: string;
  completed: boolean;
}

const App = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [inputText, setInputText] = useState("");
  const [isDark, setIsDark] = useState(false);

  const addTodo = () => {
    if (inputText.trim()) {
      const newTodo: Todo = {
        id: Date.now().toString(),
        text: inputText.trim(),
        completed: false,
      };
      setTodos([...todos, newTodo]);
      setInputText("");
    }
  };

  const toggleTodo = (id: string) => {
    setTodos(
      todos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const deleteTodo = (id: string) => {
    setTodos(todos.filter((todo) => todo.id !== id));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      addTodo();
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div
        className={`min-h-screen transition-colors duration-300 ${
          isDark ? "dark" : ""
        }`}
      >
        {/* Header with background image */}
        <div className="relative h-[200px] md:h-[300px] bg-cover bg-center bg-no-repeat bg-[url('/images/bg-mobile-light.jpg')] dark:bg-[url('/images/bg-mobile-dark.jpg')] md:bg-[url('/images/bg-desktop-light.jpg')] md:dark:bg-[url('/images/bg-desktop-dark.jpg')]">
          <div className="absolute inset-0 flex items-start justify-center pt-12 md:pt-16">
            <div className="w-full max-w-xl px-6">
              <div className="flex items-center justify-between mb-8">
                <h1 className="text-2xl md:text-4xl font-bold text-white tracking-[0.3em] md:tracking-[0.5em]">
                  TODO
                </h1>
                <button
                  onClick={() => setIsDark(!isDark)}
                  className="transition duration-300 hover:scale-110 p-1"
                >
                  <img
                    src={
                      isDark ? "/images/icon-sun.svg" : "/images/icon-moon.svg"
                    }
                    alt="Toggle theme"
                    className="w-6 h-6"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="relative -mt-24 md:-mt-32 z-10 bg-gray-100 dark:bg-gray-900 min-h-screen">
          <div className="max-w-xl mx-auto px-6 pt-8">
            {/* Todo Input */}
            <div className="relative mb-6">
              <div className="relative">
                <div className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full border-2 border-gray-300"></div>
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Create a new todo..."
                  className="w-full pl-16 pr-6 py-4 text-lg bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 placeholder-gray-500 border-none rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                />
              </div>
            </div>

            {/* Todo List */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-6">
              {todos.length === 0 ? (
                <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                  No todos found. Create your first todo above!
                </div>
              ) : (
                todos.map((todo) => (
                  <div
                    key={todo.id}
                    className="flex items-center gap-4 p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                  >
                    {/* Checkbox */}
                    <button
                      onClick={() => toggleTodo(todo.id)}
                      className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                        todo.completed
                          ? "bg-gradient-to-br from-cyan-400 to-purple-500 border-transparent"
                          : "border-gray-300 hover:border-blue-500"
                      }`}
                    >
                      {todo.completed && (
                        <img
                          src="/images/icon-check.svg"
                          alt="Completed"
                          className="w-3 h-3"
                        />
                      )}
                    </button>

                    {/* Todo text */}
                    <span
                      className={`flex-1 transition-all duration-200 ${
                        todo.completed
                          ? "line-through text-gray-400"
                          : "text-gray-800 dark:text-gray-200"
                      }`}
                    >
                      {todo.text}
                    </span>

                    {/* Delete button */}
                    <button
                      onClick={() => deleteTodo(todo.id)}
                      className="flex-shrink-0 w-6 h-6 opacity-50 hover:opacity-100 transition-opacity duration-200"
                    >
                      <img
                        src="/images/icon-cross.svg"
                        alt="Delete"
                        className="w-full h-full"
                      />
                    </button>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            {todos.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4">
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>
                    {todos.filter((t) => !t.completed).length} items left
                  </span>
                  <button
                    onClick={() => setTodos(todos.filter((t) => !t.completed))}
                    className="hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
                  >
                    Clear Completed
                  </button>
                </div>
              </div>
            )}

            <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-12">
              Drag and drop to reorder list
            </p>
          </div>
        </div>
      </div>
    </QueryClientProvider>
  );
};

export default App;
