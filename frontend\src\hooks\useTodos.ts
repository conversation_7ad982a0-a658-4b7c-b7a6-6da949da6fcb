import React from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  todoApi,
  Todo,
  CreateTodoRequest,
  UpdateTodoRequest,
  ReorderTodosRequest,
} from "../services/api";
import { useTodoStore } from "../store/todoStore";

export const useTodos = () => {
  const queryClient = useQueryClient();
  const { setTodos, addTodo, updateTodo, removeTodo, reorderTodos } =
    useTodoStore();

  // Fetch todos
  const todosQuery = useQuery({
    queryKey: ["todos"],
    queryFn: todoApi.getTodos,
  });

  // Update store when data changes
  React.useEffect(() => {
    if (todosQuery.data) {
      setTodos(todosQuery.data);
    }
  }, [todosQuery.data, setTodos]);

  // Create todo mutation
  const createTodoMutation = useMutation({
    mutationFn: todoApi.createTodo,
    onSuccess: (newTodo: Todo) => {
      addTodo(newTodo);
      queryClient.invalidateQueries({ queryKey: ["todos"] });
    },
  });

  // Update todo mutation
  const updateTodoMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTodoRequest }) =>
      todoApi.updateTodo(id, data),
    onSuccess: (updatedTodo: Todo) => {
      updateTodo(updatedTodo._id, updatedTodo);
      queryClient.invalidateQueries({ queryKey: ["todos"] });
    },
  });

  // Delete todo mutation
  const deleteTodoMutation = useMutation({
    mutationFn: todoApi.deleteTodo,
    onSuccess: (_, deletedId: string) => {
      removeTodo(deletedId);
      queryClient.invalidateQueries({ queryKey: ["todos"] });
    },
  });

  // Reorder todos mutation
  const reorderTodosMutation = useMutation({
    mutationFn: todoApi.reorderTodos,
    onSuccess: (reorderedTodos: Todo[]) => {
      reorderTodos(reorderedTodos);
      queryClient.invalidateQueries({ queryKey: ["todos"] });
    },
  });

  // Delete completed todos mutation
  const deleteCompletedMutation = useMutation({
    mutationFn: todoApi.deleteCompletedTodos,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["todos"] });
    },
  });

  return {
    // Query
    todos: todosQuery.data || [],
    isLoading: todosQuery.isLoading,
    error: todosQuery.error,

    // Mutations
    createTodo: (data: CreateTodoRequest) => createTodoMutation.mutate(data),
    updateTodo: (id: string, data: UpdateTodoRequest) =>
      updateTodoMutation.mutate({ id, data }),
    deleteTodo: (id: string) => deleteTodoMutation.mutate(id),
    reorderTodos: (data: ReorderTodosRequest) =>
      reorderTodosMutation.mutate(data),
    deleteCompleted: () => deleteCompletedMutation.mutate(),

    // Loading states
    isCreating: createTodoMutation.isPending,
    isUpdating: updateTodoMutation.isPending,
    isDeleting: deleteTodoMutation.isPending,
    isReordering: reorderTodosMutation.isPending,
    isDeletingCompleted: deleteCompletedMutation.isPending,
  };
};
