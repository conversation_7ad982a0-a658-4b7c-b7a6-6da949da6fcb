import { useTodoStore, FilterType } from '../store/todoStore';
import { useTodos } from '../hooks/useTodos';

const TodoFilters = () => {
  const { filter, setFilter, activeCount, completedCount } = useTodoStore();
  const { deleteCompleted, isDeletingCompleted } = useTodos();

  const filters: { key: FilterType; label: string }[] = [
    { key: 'all', label: 'All' },
    { key: 'active', label: 'Active' },
    { key: 'completed', label: 'Completed' },
  ];

  const handleClearCompleted = () => {
    if (completedCount() > 0) {
      deleteCompleted();
    }
  };

  return (
    <div className="bg-white dark:bg-Very-Dark-Desaturated-Blue rounded-lg shadow-lg p-4">
      <div className="flex items-center justify-between">
        {/* Items left counter */}
        <span className="text-sm text-Dark-Grayish-Blue dark:text-Dark-Grayish-Blue2">
          {activeCount()} item{activeCount() !== 1 ? 's' : ''} left
        </span>

        {/* Filter buttons */}
        <div className="flex gap-4">
          {filters.map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setFilter(key)}
              className={`text-sm font-bold transition-colors duration-200 hover:text-Very-Dark-Grayish-Blue dark:hover:text-Light-Grayish-Blue-hover ${
                filter === key
                  ? 'text-Bright-Blue'
                  : 'text-Dark-Grayish-Blue dark:text-Dark-Grayish-Blue2'
              }`}
            >
              {label}
            </button>
          ))}
        </div>

        {/* Clear completed button */}
        <button
          onClick={handleClearCompleted}
          disabled={completedCount() === 0 || isDeletingCompleted}
          className={`text-sm transition-colors duration-200 ${
            completedCount() > 0 && !isDeletingCompleted
              ? 'text-Dark-Grayish-Blue dark:text-Dark-Grayish-Blue2 hover:text-Very-Dark-Grayish-Blue dark:hover:text-Light-Grayish-Blue-hover cursor-pointer'
              : 'text-Light-Grayish-Blue dark:text-Very-Dark-Grayish-Blue3 cursor-not-allowed'
          }`}
        >
          {isDeletingCompleted ? 'Clearing...' : 'Clear Completed'}
        </button>
      </div>
    </div>
  );
};

export default TodoFilters;
