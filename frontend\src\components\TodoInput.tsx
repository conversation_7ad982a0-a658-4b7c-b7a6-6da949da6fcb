import { useState } from 'react';
import { useTodos } from '../hooks/useTodos';

const TodoInput = () => {
  const [text, setText] = useState('');
  const { createTodo, isCreating } = useTodos();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      createTodo({ text: text.trim() });
      setText('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="relative mb-6">
      <div className="relative">
        <div className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full border-2 border-Light-Grayish-Blue dark:border-Very-Dark-Grayish-Blue3"></div>
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Create a new todo..."
          disabled={isCreating}
          className="w-full pl-16 pr-6 py-4 text-lg bg-white dark:bg-Very-Dark-Desaturated-Blue text-Very-Dark-Grayish-Blue dark:text-Light-Grayish-Blue2 placeholder-Dark-Grayish-Blue dark:placeholder-Dark-Grayish-Blue2 border-none rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-Bright-Blue transition-all duration-200"
        />
      </div>
    </form>
  );
};

export default TodoInput;
