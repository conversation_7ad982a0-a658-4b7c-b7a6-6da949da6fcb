import { useEffect, useState } from "react";
import iconSun from "/images/icon-sun.svg";
import iconMoon from "/images/icon-moon.svg";

const DarkMode = () => {
  // Check localStorage or system preference for initial theme
  const [darkMode, setDarkMode] = useState(() => {
    const storedTheme = localStorage.getItem("theme");

    if (storedTheme) {
      return storedTheme === "dark";
    }

    // Default to user's system preference
    return window.matchMedia("(prefers-color-scheme: dark)").matches;
  });

  // Apply the "dark" class to <html> and save the preference
  useEffect(() => {
    const root = document.documentElement;

    if (darkMode) {
      root.classList.add("dark");
      localStorage.setItem("theme", "dark");
    } else {
      root.classList.remove("dark");
      localStorage.setItem("theme", "light");
    }
  }, [darkMode]);

  return (
    <button
      onClick={() => setDarkMode((prev) => !prev)}
      className="transition duration-300 hover:scale-110 p-1"
      aria-label="Toggle Dark Mode"
    >
      <img
        src={darkMode ? iconSun : iconMoon}
        alt={darkMode ? "Light mode icon" : "Dark mode icon"}
        className="w-6 h-6"
      />
    </button>
  );
};

export default DarkMode;
