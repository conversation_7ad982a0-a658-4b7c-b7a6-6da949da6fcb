{"version": 3, "sources": ["../../src/subscribable.ts"], "sourcesContent": ["export class Subscribable<TListener extends Function> {\n  protected listeners = new Set<TListener>()\n\n  constructor() {\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    this.listeners.add(listener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(listener)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,eAAN,MAA+C;AAAA,EAGpD,cAAc;AAFd,SAAU,YAAY,oBAAI,IAAe;AAGvC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AAAA,EAEA,UAAU,UAAiC;AACzC,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,YAAY;AAEjB,WAAO,MAAM;AACX,WAAK,UAAU,OAAO,QAAQ;AAC9B,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,eAAwB;AACtB,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EAEU,cAAoB;AAAA,EAE9B;AAAA,EAEU,gBAAsB;AAAA,EAEhC;AACF;", "names": []}