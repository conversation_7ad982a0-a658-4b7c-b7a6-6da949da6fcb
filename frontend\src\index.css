@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-Bright-Blue: hsl(220, 98%, 61%);
  --color-check-Background: linear-gradient hsl(192, 100%, 67%) to hsl(280, 87%, 65%);
  --color-Very-Light-Gray: hsl(0, 0%, 98%);
  --color-Very-Light-Grayish-Blue: hsl(236, 33%, 92%);
  --color-Light-Grayish-Blue: hsl(233, 11%, 84%);
  --color-Dark-Grayish-Blue: hsl(236, 9%, 61%);
  --color-Very-Dark-Grayish-Blue: hsl(235, 19%, 35%);
  --color-Very-Dark-Blue: hsl(235, 21%, 11%);
  --color-Very-Dark-Desaturated-Blue: hsl(235, 24%, 19%);
  --color-Light-Grayish-Blue2: hsl(234, 39%, 85%);
  --color-Light-Grayish-Blue-hover: hsl(236, 33%, 92%);
  --color-Dark-Grayish-Blue2: hsl(234, 11%, 52%);
  --color-Very-Dark-Grayish-Blue2: hsl(233, 14%, 35%);
  --color-Very-Dark-Grayish-Blue3: hsl(237, 14%, 26%);
}

:root {
  font-family: "Josefin Sans", sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  @apply bg-Very-Light-Gray dark:bg-Very-Dark-Blue;


  margin: 0;
  padding: 0;
  min-width: 20rem;
  /* max-width: 90rem; */
  width: 100%;
  min-height: 100vh;
  margin-inline: auto;
  /* display: flex;
  justify-content: center;
  align-items: center; */
  font-size: 18px;
  /* background: var(--color-Very-Dark-Blue); */
}