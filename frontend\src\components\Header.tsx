import DarkMode from "./DarkMode";

const Header = () => {
  return (
    <div className="relative min-w-full h-[200px] md:h-[300px] bg-cover bg-center bg-no-repeat transition-all duration-300 bg-[url(/images/bg-mobile-light.jpg)] dark:bg-[url(/images/bg-mobile-dark.jpg)] md:bg-[url(/images/bg-desktop-light.jpg)] md:dark:bg-[url(/images/bg-desktop-dark.jpg)]">
      <div className="absolute inset-0 flex items-start justify-center pt-12 md:pt-16">
        <div className="w-full max-w-xl px-6">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl md:text-4xl font-bold text-white tracking-[0.3em] md:tracking-[0.5em]">
              TODO
            </h1>
            <DarkMode />
          </div>
        </div>
      </div>
    </div>
  );
};
export default Header;
