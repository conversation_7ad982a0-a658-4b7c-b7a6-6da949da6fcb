import bgDesktopDark from '/images/bg-desktop-dark.jpg';
import bgDesktopLight from '/images/bg-desktop-light.jpg';
import bgMobileDark from '/images/bg-mobile-dark.jpg';
import bgMobileLight from '/images/bg-mobile-light.jpg';

const Header = () => {

  //  const bgImage = darkMode
  //   ? window.innerWidth >= 768
  //     ? bgDesktopDark
  //     : bgMobileDark
  //   : window.innerWidth >= 768
  //     ? bgDesktopLight
  //     : bgMobileLight;


  return (
    <div
      className="min-w-full h-[10rem] bg-auto bg-no-repeat transition-all duration-300 dark:bg-[url(/images/bg-desktop-dark.jpg)] md:bg-[url(/images/bg-desktop-light.jpg)] lg:dark:bg-[url(/images/bg-mobile-dark.jpg)] md:bg-[url(/images/bg-mobile-light.jpg)]"
      // style={{ backgroundImage: `url(${bgImage})` }}
    >
      
    </div>
  )
}
export default Header